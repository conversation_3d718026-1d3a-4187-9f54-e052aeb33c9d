<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class CompanySetting extends Model
{
    protected $fillable = [
        'logo_path',
        'theme_color',
        'company_name',
        'address',
        'contact_info',
        'menu_layout',
        'menu_items',
        'menu_collapsible',
    ];

    protected $casts = [
        'menu_items' => 'array',
        'menu_collapsible' => 'boolean',
    ];

    /**
     * Get the company settings (singleton pattern)
     */
    public static function getSettings()
    {
        $settings = self::first();

        if (!$settings) {
            $settings = self::create([
                'company_name' => 'Medical Shop',
                'theme_color' => '#3B82F6',
                'address' => '',
                'contact_info' => '',
                'menu_layout' => 'left',
                'menu_collapsible' => true,
                'menu_items' => self::getDefaultMenuItems(),
            ]);
        }

        return $settings;
    }

    /**
     * Get the logo URL
     */
    public function getLogoUrlAttribute()
    {
        if ($this->logo_path && Storage::exists($this->logo_path)) {
            return Storage::url($this->logo_path);
        }

        return null;
    }

    /**
     * Update company settings
     */
    public static function updateSettings(array $data)
    {
        $settings = self::getSettings();
        $settings->update($data);
        return $settings;
    }
}
