<?php

use App\Http\Controllers\BranchController;
use App\Http\Controllers\CompanySettingController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Branch Management Routes
Route::resource('branches', BranchController::class);
Route::post('/branches/set-active', [BranchController::class, 'setActive'])->name('branches.set-active');

// Company Settings Routes
Route::get('/company/settings', [CompanySettingController::class, 'show'])->name('company.settings');
Route::put('/company/settings', [CompanySettingController::class, 'update'])->name('company.settings.update');

// Demo Routes
Route::get('/demo/load-more', function () {
    return view('demo.load-more');
})->name('demo.load-more');
