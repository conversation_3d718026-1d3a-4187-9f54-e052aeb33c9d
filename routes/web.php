<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CompanySettingController;

// Authentication Routes
Route::get('/', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login'])->name('login.post');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Registration Routes (for super admin setup)
Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register'])->name('register.post');

// Protected Routes
Route::middleware(['auth', 'branch.context'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Branch switching (Admin only)
    Route::get('/switch-branch/{branch}', function ($branchId) {
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Access denied');
        }

        if ($branchId === 'all') {
            session()->forget(['active_branch_id', 'active_branch_name']);
        } else {
            $branch = \App\Models\Branch::findOrFail($branchId);
            session([
                'active_branch_id' => $branch->id,
                'active_branch_name' => $branch->name
            ]);
        }

        return redirect()->back()->with('success', 'Branch context updated successfully');
    })->name('switch-branch');

    // Company Settings (Admin only)
    Route::prefix('company-settings')->name('company-settings.')->group(function () {
        Route::get('/', [CompanySettingController::class, 'index'])->name('index');
        Route::put('/', [CompanySettingController::class, 'update'])->name('update');
        Route::post('/reset-menu', [CompanySettingController::class, 'resetMenu'])->name('reset-menu');
    });

    // Placeholder routes for menu items (to be implemented)
    Route::get('/medicines', function () {
        return view('placeholder', ['title' => 'Medicines', 'message' => 'Medicines management coming soon...']);
    })->name('medicines.index');

    Route::get('/suppliers', function () {
        return view('placeholder', ['title' => 'Suppliers', 'message' => 'Suppliers management coming soon...']);
    })->name('suppliers.index');

    Route::get('/branches', function () {
        return view('placeholder', ['title' => 'Branches', 'message' => 'Branches management coming soon...']);
    })->name('branches.index');

    Route::get('/users', function () {
        return view('placeholder', ['title' => 'Users', 'message' => 'Users management coming soon...']);
    })->name('users.index');

    Route::get('/purchases', function () {
        return view('placeholder', ['title' => 'Purchases', 'message' => 'Purchases management coming soon...']);
    })->name('purchases.index');

    Route::get('/sales', function () {
        return view('placeholder', ['title' => 'Sales', 'message' => 'Sales management coming soon...']);
    })->name('sales.index');

    Route::get('/customer-returns', function () {
        return view('placeholder', ['title' => 'Customer Returns', 'message' => 'Customer returns management coming soon...']);
    })->name('customer-returns.index');

    Route::get('/supplier-returns', function () {
        return view('placeholder', ['title' => 'Supplier Returns', 'message' => 'Supplier returns management coming soon...']);
    })->name('supplier-returns.index');

    Route::get('/reports', function () {
        return view('placeholder', ['title' => 'Reports', 'message' => 'Reports dashboard coming soon...']);
    })->name('reports.index');

    Route::get('/reports/stock', function () {
        return view('placeholder', ['title' => 'Stock Report', 'message' => 'Stock reports coming soon...']);
    })->name('reports.stock');

    Route::get('/reports/sales', function () {
        return view('placeholder', ['title' => 'Sales Report', 'message' => 'Sales reports coming soon...']);
    })->name('reports.sales');

    Route::get('/reports/purchases', function () {
        return view('placeholder', ['title' => 'Purchase Report', 'message' => 'Purchase reports coming soon...']);
    })->name('reports.purchases');
});
