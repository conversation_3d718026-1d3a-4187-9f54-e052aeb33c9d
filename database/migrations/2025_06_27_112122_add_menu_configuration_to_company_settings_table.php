<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('company_settings', function (Blueprint $table) {
            $table->enum('menu_layout', ['left', 'top'])->default('left')->after('contact_info');
            $table->json('menu_items')->nullable()->after('menu_layout');
            $table->boolean('menu_collapsible')->default(true)->after('menu_items');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('company_settings', function (Blueprint $table) {
            $table->dropColumn(['menu_layout', 'menu_items', 'menu_collapsible']);
        });
    }
};
