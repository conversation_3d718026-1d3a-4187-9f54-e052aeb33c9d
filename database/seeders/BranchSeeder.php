<?php

namespace Database\Seeders;

use App\Models\Branch;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $branches = [
            [
                'name' => 'Main Branch',
                'address' => '123 Medical Street, Downtown, City 12345',
                'is_active' => true,
            ],
            [
                'name' => 'North Branch',
                'address' => '456 Health Avenue, North District, City 12346',
                'is_active' => true,
            ],
            [
                'name' => 'South Branch',
                'address' => '789 Pharmacy Road, South District, City 12347',
                'is_active' => true,
            ],
            [
                'name' => 'East Branch',
                'address' => '321 Medicine Lane, East District, City 12348',
                'is_active' => false, // Inactive branch for testing
            ],
        ];

        foreach ($branches as $branch) {
            Branch::create($branch);
        }
    }
}
