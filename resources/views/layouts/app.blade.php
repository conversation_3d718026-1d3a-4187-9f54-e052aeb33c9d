<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Medical Shop Inventory')</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">Medical Shop</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Branch Selector -->
                    <x-branch-selector />

                    <!-- Navigation Links -->
                    <a href="{{ route('branches.index') }}"
                       class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        Branches
                    </a>
                    <a href="{{ route('demo.load-more') }}"
                       class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        Load More Demo
                    </a>
                    <a href="{{ route('company.settings') }}"
                       class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                        Settings
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        @yield('content')
    </main>

    <!-- Load More Pagination Component -->
    <script>
        // Global Load More functionality
        window.loadMorePagination = function(initialData = [], perPage = 10) {
            return {
                items: initialData,
                displayedItems: [],
                currentPage: 1,
                perPage: perPage,
                loading: false,
                hasMore: true,

                init() {
                    this.loadMore();
                },

                loadMore() {
                    if (this.loading || !this.hasMore) return;
                    
                    this.loading = true;
                    
                    // Simulate API call delay
                    setTimeout(() => {
                        const startIndex = (this.currentPage - 1) * this.perPage;
                        const endIndex = startIndex + this.perPage;
                        const newItems = this.items.slice(startIndex, endIndex);
                        
                        this.displayedItems = [...this.displayedItems, ...newItems];
                        this.currentPage++;
                        this.hasMore = endIndex < this.items.length;
                        this.loading = false;
                    }, 500);
                },

                reset() {
                    this.displayedItems = [];
                    this.currentPage = 1;
                    this.hasMore = true;
                    this.loadMore();
                }
            }
        }

        // AJAX Load More for server-side pagination
        window.ajaxLoadMore = function(url, perPage = 10) {
            return {
                items: [],
                currentPage: 1,
                perPage: perPage,
                loading: false,
                hasMore: true,
                error: null,

                async init() {
                    await this.loadMore();
                },

                async loadMore() {
                    if (this.loading || !this.hasMore) return;
                    
                    this.loading = true;
                    this.error = null;
                    
                    try {
                        const response = await fetch(`${url}?page=${this.currentPage}&per_page=${this.perPage}`, {
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'Accept': 'application/json',
                            }
                        });
                        
                        if (!response.ok) throw new Error('Failed to load data');
                        
                        const data = await response.json();
                        
                        this.items = [...this.items, ...data.data];
                        this.currentPage++;
                        this.hasMore = data.has_more || false;
                        
                    } catch (error) {
                        this.error = error.message;
                    } finally {
                        this.loading = false;
                    }
                },

                async reset() {
                    this.items = [];
                    this.currentPage = 1;
                    this.hasMore = true;
                    this.error = null;
                    await this.loadMore();
                }
            }
        }
    </script>

    @stack('scripts')
</body>
</html>
