<div x-data="{ sidebarOpen: false }" class="flex h-screen overflow-hidden">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
         :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }">
        
        <!-- Logo -->
        <div class="flex items-center justify-center h-16 bg-blue-600 text-white">
            <div class="flex items-center">
                <i class="fas fa-clinic-medical text-2xl mr-2"></i>
                <span class="text-xl font-bold">{{ $settings->company_name }}</span>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mt-5 px-2 flex-1 overflow-y-auto">
            <div class="space-y-1">
                @foreach($menuItems as $item)
                    @if(($item['type'] ?? '') === 'section')
                        <!-- Section Header -->
                        <div class="mt-6">
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">{{ $item['name'] }}</h3>
                            <div class="mt-2 space-y-1">
                                @foreach($item['children'] ?? [] as $child)
                                    <a href="{{ route($child['route']) }}" 
                                       class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs($child['route'].'*') ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                                        <i class="{{ $child['icon'] }} mr-3 text-lg"></i>
                                        {{ $child['name'] }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <!-- Single Menu Item -->
                        <a href="{{ route($item['route']) }}" 
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs($item['route']) ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                            <i class="{{ $item['icon'] }} mr-3 text-lg"></i>
                            {{ $item['name'] }}
                        </a>
                    @endif
                @endforeach
            </div>
        </nav>

        <!-- User Info -->
        <div class="absolute bottom-0 w-full p-4 border-t border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">{{ substr(auth()->user()->name, 0, 1) }}</span>
                    </div>
                </div>
                <div class="ml-3 flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">{{ auth()->user()->name }}</p>
                    <p class="text-xs text-gray-500 truncate">{{ ucfirst(auth()->user()->role) }}</p>
                </div>
                <form method="POST" action="{{ route('logout') }}" class="ml-2">
                    @csrf
                    <button type="submit" class="text-gray-400 hover:text-gray-600" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top bar -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-4 py-3">
                <div class="flex items-center">
                    <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="ml-4 lg:ml-0 text-xl font-semibold text-gray-900">@yield('page-title', 'Dashboard')</h1>
                </div>
                
                <!-- Branch Selector for Admin -->
                @if(auth()->user()->isAdmin())
                <div class="flex items-center space-x-4">
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100">
                            <i class="fas fa-building mr-2"></i>
                            {{ session('active_branch_name', 'All Branches') }}
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50">
                            <a href="{{ route('switch-branch', 'all') }}" 
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                All Branches
                            </a>
                            @foreach(\App\Models\Branch::where('is_active', true)->get() as $branch)
                            <a href="{{ route('switch-branch', $branch->id) }}" 
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                {{ $branch->name }}
                            </a>
                            @endforeach
                        </div>
                    </div>
                </div>
                @else
                <div class="flex items-center text-sm text-gray-600">
                    <i class="fas fa-building mr-2"></i>
                    {{ auth()->user()->branch->name ?? 'No Branch' }}
                </div>
                @endif
            </div>
        </header>

        <!-- Page content -->
        <main class="flex-1 overflow-y-auto p-6">
            @if(session('success'))
            <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                {{ session('success') }}
            </div>
            @endif

            @if(session('error'))
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {{ session('error') }}
            </div>
            @endif

            @yield('content')
        </main>
    </div>

    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" @click="sidebarOpen = false" 
         class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"></div>
</div>
