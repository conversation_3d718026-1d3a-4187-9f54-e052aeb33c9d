<div class="min-h-screen bg-gray-50">
    <!-- Top Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200" x-data="{ mobileMenuOpen: false }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Brand -->
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-clinic-medical text-2xl text-blue-600 mr-2"></i>
                        <span class="text-xl font-bold text-gray-900">{{ $settings->company_name }}</span>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    @foreach($menuItems as $item)
                        @if(($item['type'] ?? '') === 'section')
                            <!-- Dropdown Menu -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" 
                                        class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none">
                                    {{ $item['name'] }}
                                    <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                </button>
                                <div x-show="open" @click.away="open = false" 
                                     class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50">
                                    @foreach($item['children'] ?? [] as $child)
                                    <a href="{{ route($child['route']) }}" 
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 {{ request()->routeIs($child['route'].'*') ? 'bg-blue-50 text-blue-700' : '' }}">
                                        <i class="{{ $child['icon'] }} mr-3"></i>
                                        {{ $child['name'] }}
                                    </a>
                                    @endforeach
                                </div>
                            </div>
                        @else
                            <!-- Single Menu Item -->
                            <a href="{{ route($item['route']) }}" 
                               class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs($item['route']) ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-700 hover:text-gray-900' }}">
                                <i class="{{ $item['icon'] }} mr-2"></i>
                                {{ $item['name'] }}
                            </a>
                        @endif
                    @endforeach
                </div>

                <!-- Right side - User menu and branch selector -->
                <div class="flex items-center space-x-4">
                    <!-- Branch Selector for Admin -->
                    @if(auth()->user()->isAdmin())
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100">
                            <i class="fas fa-building mr-2"></i>
                            {{ session('active_branch_name', 'All Branches') }}
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50">
                            <a href="{{ route('switch-branch', 'all') }}" 
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                All Branches
                            </a>
                            @foreach(\App\Models\Branch::where('is_active', true)->get() as $branch)
                            <a href="{{ route('switch-branch', $branch->id) }}" 
                               class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                {{ $branch->name }}
                            </a>
                            @endforeach
                        </div>
                    </div>
                    @else
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-building mr-2"></i>
                        {{ auth()->user()->branch->name ?? 'No Branch' }}
                    </div>
                    @endif

                    <!-- User Menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium">{{ substr(auth()->user()->name, 0, 1) }}</span>
                            </div>
                        </button>
                        <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50">
                            <div class="px-4 py-2 border-b border-gray-200">
                                <p class="text-sm font-medium text-gray-900">{{ auth()->user()->name }}</p>
                                <p class="text-xs text-gray-500">{{ ucfirst(auth()->user()->role) }}</p>
                            </div>
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i>
                                    Logout
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div x-show="mobileMenuOpen" class="md:hidden bg-white border-t border-gray-200">
            <div class="px-2 pt-2 pb-3 space-y-1">
                @foreach($menuItems as $item)
                    @if(($item['type'] ?? '') === 'section')
                        <div class="px-3 py-2">
                            <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">{{ $item['name'] }}</h3>
                            <div class="mt-1 space-y-1">
                                @foreach($item['children'] ?? [] as $child)
                                <a href="{{ route($child['route']) }}" 
                                   class="flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs($child['route'].'*') ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                                    <i class="{{ $child['icon'] }} mr-3"></i>
                                    {{ $child['name'] }}
                                </a>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <a href="{{ route($item['route']) }}" 
                           class="flex items-center px-3 py-2 text-sm font-medium rounded-md {{ request()->routeIs($item['route']) ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }}">
                            <i class="{{ $item['icon'] }} mr-3"></i>
                            {{ $item['name'] }}
                        </a>
                    @endif
                @endforeach
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <h1 class="text-2xl font-bold text-gray-900">@yield('page-title', 'Dashboard')</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        @if(session('success'))
        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {{ session('error') }}
        </div>
        @endif

        @yield('content')
    </main>
</div>
