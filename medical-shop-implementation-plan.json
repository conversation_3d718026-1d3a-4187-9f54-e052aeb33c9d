{"project": "Laravel 12 Medical Shop Inventory System", "overview": "Simple inventory management system with multi-branch support, role-based access, and core stock management functionality", "implementation_sequence": [{"step": 1, "module": "Project Setup & Authentication", "description": "Initialize Laravel project with authentication scaffolding", "tasks": ["Create new Laravel 12 project", "Install Laravel Breeze for authentication", "Set up database configuration", "Install required packages: Spatie Permission, DomPDF, Tailwind CSS"], "commands": ["composer create-project laravel/laravel medical-shop", "composer require laravel/breeze spatie/laravel-permission barryvdh/laravel-dompdf", "php artisan breeze:install blade", "npm install && npm run build"], "deliverables": ["Basic Laravel setup with authentication"]}, {"step": 2, "module": "Company Settings Management", "description": "Super Admin can manage company-wide settings", "models": ["CompanySetting"], "migrations": ["create_company_settings_table: id, logo_path, theme_color, company_name, address, contact_info, timestamps"], "controllers": ["CompanySettingController"], "views": ["company/settings.blade.php"], "routes": ["GET /admin/company/settings", "POST /admin/company/settings"], "features": ["File upload for logo with validation", "Form validation for all fields", "Settings persistence in database", "Theme color picker integration"], "prompt": "Create CompanySetting model with logo upload, theme_color, company_name, address, contact_info fields. Build controller with show/update methods, create Blade form with file upload, add validation rules, and implement logo storage in public/uploads/company directory."}, {"step": 3, "module": "Branch Management System", "description": "Multi-branch support with session-based context switching", "models": ["Branch"], "migrations": ["create_branches_table: id, name, address, is_active, created_at, updated_at"], "controllers": ["BranchController"], "middleware": ["BranchContextMiddleware"], "views": ["branches/index.blade.php", "branches/create.blade.php", "branches/edit.blade.php", "partials/branch-selector.blade.php"], "routes": ["resource /admin/branches", "POST /set-active-branch"], "features": ["CRUD operations for branches", "Session-based active branch storage", "Branch selector dropdown in navigation", "Automatic branch context for all operations"], "prompt": "Create Branch model with name, address, is_active fields. Implement full CRUD with BranchController. Create middleware to set active branch in session. Add branch selector dropdown component that appears in main navigation. Ensure branch context persists across requests."}, {"step": 4, "module": "Core Inventory Models", "description": "Define all core models with branch scoping and relationships", "models": ["Medicine", "Supplier", "Purchase", "Sale", "CustomerReturn", "SupplierReturn", "StockMovement"], "migrations": ["create_medicines_table: id, branch_id, name, sku, price, stock_quantity, reorder_threshold, description, timestamps", "create_suppliers_table: id, branch_id, name, contact_person, phone, email, address, timestamps", "create_purchases_table: id, branch_id, supplier_id, invoice_number, purchase_date, total_amount, timestamps", "create_purchase_items_table: id, purchase_id, medicine_id, quantity, unit_price, total_price, timestamps", "create_sales_table: id, branch_id, receipt_number, sale_date, total_amount, timestamps", "create_sale_items_table: id, sale_id, medicine_id, quantity, unit_price, total_price, timestamps", "create_customer_returns_table: id, branch_id, return_number, return_date, total_amount, reason, timestamps", "create_customer_return_items_table: id, customer_return_id, medicine_id, quantity, unit_price, total_price, timestamps", "create_supplier_returns_table: id, branch_id, supplier_id, return_number, return_date, total_amount, reason, timestamps", "create_supplier_return_items_table: id, supplier_return_id, medicine_id, quantity, unit_price, total_price, timestamps", "create_stock_movements_table: id, branch_id, medicine_id, movement_type, quantity, reference_type, reference_id, notes, created_at"], "features": ["Global scope for branch_id on all models", "Eloquent relationships between models", "Model factories for testing", "Automatic stock movement logging"], "prompt": "Create all core models with proper relationships and branch_id foreign keys. Add global scopes to filter by active branch. Define relationships: Medicine belongsTo Branch, Purchase hasMany PurchaseItems, Sale hasMany SaleItems, etc. Create StockMovement model to log all stock changes with movement_type enum (purchase, sale, customer_return, supplier_return)."}, {"step": 5, "module": "Stock Management Logic", "description": "Implement automatic stock updates and movement tracking", "services": ["StockService"], "observers": ["PurchaseObserver", "SaleObserver", "CustomerReturnObserver", "SupplierReturnObserver"], "features": ["Automatic stock increment on purchase/customer return", "Automatic stock decrement on sale/supplier return", "Stock movement logging for audit trail", "Low stock alerts and notifications"], "prompt": "Create StockService class to handle all stock operations. Implement model observers to automatically update medicine stock_quantity when purchases, sales, or returns are created. Log every stock change in StockMovement table with proper reference tracking. Add methods to check low stock items (stock <= reorder_threshold)."}, {"step": 6, "module": "Purchase Management", "description": "Complete purchase workflow with stock updates", "controllers": ["PurchaseController"], "views": ["purchases/index.blade.php", "purchases/create.blade.php", "purchases/show.blade.php", "purchases/edit.blade.php"], "routes": ["resource /purchases"], "features": ["Multi-item purchase creation", "Supplier selection", "Automatic stock increment", "Purchase invoice generation"], "prompt": "Build complete purchase management system. Create PurchaseController with CRUD operations. Design purchase form with dynamic item addition (medicine selection, quantity, price). Implement purchase creation that automatically updates medicine stock and logs movements. Add purchase listing with search and filter capabilities."}, {"step": 7, "module": "Sales Management", "description": "Point of sale system with stock deduction", "controllers": ["SaleController"], "views": ["sales/index.blade.php", "sales/create.blade.php", "sales/show.blade.php"], "routes": ["resource /sales"], "features": ["Multi-item sale creation", "Stock availability checking", "Automatic stock decrement", "Receipt generation"], "prompt": "Create sales management system with SaleController. Build POS-style interface for creating sales with medicine search, quantity input, and real-time total calculation. Implement stock validation (prevent overselling) and automatic stock deduction. Generate printable receipts and maintain sales history."}, {"step": 8, "module": "Returns Management", "description": "Handle customer and supplier returns with stock adjustments", "controllers": ["CustomerReturnController", "SupplierReturnController"], "views": ["returns/customer/index.blade.php", "returns/customer/create.blade.php", "returns/supplier/index.blade.php", "returns/supplier/create.blade.php"], "routes": ["resource /returns/customer", "resource /returns/supplier"], "features": ["Customer return processing (stock increment)", "Supplier return processing (stock decrement)", "Return reason tracking", "Return documentation"], "prompt": "Implement customer and supplier return systems. Create controllers for both return types. Build forms to select items for return with quantities and reasons. Implement stock adjustments: customer returns increase stock, supplier returns decrease stock. Add return tracking and history views."}, {"step": 9, "module": "Dashboard & Analytics", "description": "Main dashboard with key metrics and insights", "controllers": ["DashboardController"], "views": ["dashboard.blade.php"], "routes": ["GET /dashboard"], "features": ["Total medicines count", "Total stock value calculation", "Low stock items count and list", "Recent transactions summary", "Quick action buttons", "Branch-specific metrics"], "prompt": "Create comprehensive dashboard showing key inventory metrics. Display total medicines, calculated stock value (sum of stock_quantity * price), count of low-stock items, recent purchases/sales/returns. Add quick navigation to common actions. Include charts for stock levels and transaction trends using Chart.js."}, {"step": 10, "module": "Medicine Management & Filtering", "description": "Medicine CRUD with advanced filtering and search", "controllers": ["MedicineController"], "views": ["medicines/index.blade.php", "medicines/create.blade.php", "medicines/edit.blade.php", "medicines/show.blade.php"], "routes": ["resource /medicines"], "features": ["Medicine CRUD operations", "Search by name or SKU", "Filter by stock level", "Low stock highlighting", "Bulk operations", "Medicine image upload"], "prompt": "Build complete medicine management system. Create MedicineController with full CRUD. Implement advanced search and filtering (name, SKU, stock level, supplier). Add visual indicators for low stock items (red highlighting). Include bulk actions for updating prices or reorder thresholds. Add medicine image upload functionality."}, {"step": 11, "module": "Reports System", "description": "Comprehensive reporting with filtering and export", "controllers": ["ReportController"], "views": ["reports/index.blade.php", "reports/stock.blade.php", "reports/purchases.blade.php", "reports/sales.blade.php", "reports/returns.blade.php"], "routes": ["GET /reports", "GET /reports/stock", "GET /reports/purchases", "GET /reports/sales", "GET /reports/returns", "POST /reports/export"], "features": ["Stock level reports", "Purchase reports with date range", "Sales reports with analytics", "Returns tracking reports", "Advanced filtering options", "PDF export functionality", "Excel export option"], "prompt": "Create comprehensive reporting system. Build ReportController with methods for different report types. Implement filtering by medicine name, SKU, invoice/receipt numbers, date ranges. Add PDF export using DomPDF for all reports. Create clean, printable report layouts with company branding. Include summary statistics in each report."}, {"step": 12, "module": "Role-Based Access Control", "description": "Implement user roles and permissions using <PERSON><PERSON>", "models": ["User (extended)"], "controllers": ["UserController", "RoleController"], "middleware": ["RoleMiddleware"], "views": ["users/index.blade.php", "users/create.blade.php", "roles/index.blade.php"], "routes": ["resource /admin/users", "resource /admin/roles"], "roles": ["super_admin: Full system access, company settings, all branches", "branch_manager: Branch-specific access, inventory management, reports"], "permissions": ["manage_company_settings", "manage_branches", "manage_users", "manage_inventory", "view_reports", "export_data"], "features": ["Role assignment to users", "Permission-based route protection", "Branch-specific access control", "User management interface"], "prompt": "Implement role-based access using <PERSON><PERSON> Permission. Create super_admin and branch_manager roles. Super admin can access company settings, manage all branches and users. Branch manager can only access their assigned branch data. Add middleware to protect routes based on roles/permissions. Create user management interface for super admin."}, {"step": 13, "module": "UI/UX Polish & Testing", "description": "Final touches, testing, and deployment preparation", "features": ["Responsive design optimization", "Loading states and animations", "Form validation improvements", "Error handling and user feedback", "Performance optimization", "Basic testing setup"], "tasks": ["Add loading spinners and progress indicators", "Implement toast notifications for user actions", "Add confirmation dialogs for destructive actions", "Optimize database queries with eager loading", "Add basic feature tests for critical workflows", "Create seeder for demo data", "Add backup and restore functionality"], "prompt": "Polish the application with improved UX. Add loading states, toast notifications using Alpine.js, confirmation dialogs for deletions. Optimize database queries and add indexes. Create comprehensive seeders for demo data. Write basic feature tests for purchase, sale, and return workflows. Add data backup/restore functionality."}], "technical_requirements": {"laravel_version": "12.x", "php_version": "8.2+", "database": "MySQL 8.0+", "frontend": "Blade + Tailwind CSS + Alpine.js", "packages": ["spatie/laravel-permission", "barryvdh/laravel-dompdf", "maatwebsite/excel", "intervention/image"]}, "database_design": {"core_tables": ["company_settings", "branches", "users", "medicines", "suppliers", "purchases", "purchase_items", "sales", "sale_items", "customer_returns", "customer_return_items", "supplier_returns", "supplier_return_items", "stock_movements"], "key_relationships": ["Branch hasMany Medicine", "Purchase belongsTo Supplier", "Purchase hasMany PurchaseItems", "Sale hasMany SaleItems", "Medicine hasMany StockMovements"]}, "implementation_notes": ["Each step builds upon the previous ones", "Test each module thoroughly before proceeding", "Use Laravel's built-in validation and authorization", "Implement proper error handling throughout", "Follow <PERSON>vel naming conventions and best practices", "Use database transactions for stock operations", "Add proper indexes for performance", "Implement soft deletes where appropriate"]}